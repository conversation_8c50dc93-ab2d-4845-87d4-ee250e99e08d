package com.lx.pl.config;

import com.lx.pl.client.GeminiApiClient;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.concurrent.TimeUnit;

/**
 * Gemini API客户端配置
 * 基于fal.ai nano-banana API
 *
 * <AUTHOR>
 */
@Configuration
public class GeminiApiClientConfig {

    @Autowired
    private GeminiConfig geminiConfig;

    @Bean
    public GeminiApiClient geminiApiClient() {
        // 配置HTTP日志拦截器
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        // 配置OkHttpClient
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(geminiConfig.getConnectTimeoutSeconds() != null ? 
                        geminiConfig.getConnectTimeoutSeconds() : 30, TimeUnit.SECONDS)
                .readTimeout(geminiConfig.getReadTimeoutSeconds() != null ? 
                        geminiConfig.getReadTimeoutSeconds() : 60, TimeUnit.SECONDS)
                .writeTimeout(geminiConfig.getWriteTimeoutSeconds() != null ? 
                        geminiConfig.getWriteTimeoutSeconds() : 60, TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor)
                .build();

        // 配置Retrofit
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(geminiConfig.getBaseUrl())
                .client(okHttpClient)
                .addConverterFactory(JacksonConverterFactory.create())
                .build();

        return retrofit.create(GeminiApiClient.class);
    }
}
