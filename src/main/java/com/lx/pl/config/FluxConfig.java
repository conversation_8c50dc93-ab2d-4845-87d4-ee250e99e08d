package com.lx.pl.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Flux API配置类
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "flux.api")
public class FluxConfig {

    /**
     * Black Forest Labs API的基础URL
     */
    private String baseUrl = "https://api.bfl.ai";

    /**
     * BFL API密钥
     */
    private String apiKey;

    /**
     * 回调URL（如果使用webhook）
     */
    private String callbackUrl;

    /**
     * 默认超时时间（秒）
     */
    private Integer defaultTimeout = 300;

    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;

    /**
     * 默认输出格式
     */
    private String defaultOutputFormat = "png";

    /**
     * 默认安全容忍度
     */
    private Integer defaultSafetyTolerance = 2;

    /**
     * 是否启用prompt增强
     */
    private Boolean promptUpsamplingEnabled = false;

    /**
     * 最大并发任务数（默认10个）
     */
    private Integer maxConcurrentJobs = 999;

    /**
     * 任务状态轮询间隔（秒，默认3秒）
     */
    private Integer pollingIntervalSeconds = 3;

    /**
     * 首次轮询延迟时间（秒，默认2秒）
     */
    private Integer firstDelaySeconds = 2;

    /**
     * 最大轮询次数（默认100次，约5分钟）
     */
    private Integer maxPollingAttempts = 100;

    /**
     * 余额告警阈值
     */
    private Double balanceAlarmThreshold = 10.0;
}
