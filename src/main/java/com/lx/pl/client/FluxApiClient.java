package com.lx.pl.client;

import com.lx.pl.dto.flux.FluxRequest;
import com.lx.pl.dto.flux.FluxResponse;
import retrofit2.Call;
import retrofit2.http.*;

/**
 * Flux API客户端接口
 *
 * <AUTHOR>
 */
public interface FluxApiClient {

    /**
     * 创建Flux Kontext Pro图像生成任务
     *
     * @param apiKey  API密钥
     * @param request 请求参数
     * @return 创建任务响应
     */
    @POST("v1/flux-kontext-pro")
    Call<FluxResponse.CreateTaskResponse> createKontextProTask(
            @Header("x-key") String apiKey,
            @Body FluxRequest.KontextProRequest request
    );

    /**
     * 创建Flux Kontext Max图像生成任务
     *
     * @param apiKey  API密钥
     * @param request 请求参数
     * @return 创建任务响应
     */
    @POST("v1/flux-kontext-max")
    Call<FluxResponse.CreateTaskResponse> createKontextMaxTask(
            @Header("x-key") String apiKey,
            @Body FluxRequest.KontextProRequest request
    );

    /**
     * 获取任务结果
     *
     * @param taskId 任务ID
     * @return 任务状态响应
     */
    @GET("v1/get_result")
    Call<FluxResponse.TaskStatusResponse> getTaskResult(
            @Query("id") String taskId
    );

    /**
     * 通过pollingUrl获取任务结果
     *
     * @param pollingUrl 轮询URL
     * @return 任务状态响应
     */
    @GET
    Call<FluxResponse.TaskStatusResponse> getTaskResultByPollingUrl(
            @Url String pollingUrl
    );

    /**
     * 创建FLUX 1.1 Pro图像生成任务
     *
     * @param apiKey  API密钥
     * @param request 请求参数
     * @return 创建任务响应
     */
    @POST("v1/flux-pro-1.1")
    Call<FluxResponse.CreateTaskResponse> createFlux11ProTask(
            @Header("x-key") String apiKey,
            @Body FluxRequest request
    );

    /**
     * 创建FLUX.1 Pro图像生成任务
     *
     * @param apiKey  API密钥
     * @param request 请求参数
     * @return 创建任务响应
     */
    @POST("v1/flux-pro")
    Call<FluxResponse.CreateTaskResponse> createFluxProTask(
            @Header("x-key") String apiKey,
            @Body FluxRequest request
    );

    /**
     * 创建FLUX.1 Dev图像生成任务
     *
     * @param apiKey  API密钥
     * @param request 请求参数
     * @return 创建任务响应
     */
    @POST("v1/flux-dev")
    Call<FluxResponse.CreateTaskResponse> createFluxDevTask(
            @Header("x-key") String apiKey,
            @Body FluxRequest request
    );
}
