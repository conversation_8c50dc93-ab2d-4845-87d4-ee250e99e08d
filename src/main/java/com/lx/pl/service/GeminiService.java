package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.client.GeminiApiClient;
import com.lx.pl.config.GeminiConfig;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.ModelRightsVerifyResult;
import com.lx.pl.dto.gemini.GeminiRequest;
import com.lx.pl.dto.gemini.GeminiResponse;
import com.lx.pl.dto.mq.TaskPollingVo;
import com.lx.pl.enums.FeaturesType;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.TaskTypeForMq;
import com.lx.pl.exception.LogicException;
import com.lx.pl.exception.GeminiApiException;
import com.lx.pl.util.GeminiErrorParser;
import com.lx.pl.mq.producer.NormalMessageProducer;
import com.lx.pl.util.AspectRatioUtils;
import com.lx.pl.dto.mq.MjImageProcessVo;
import com.lx.pl.mq.message.CommonMqMessage;
import com.lx.pl.mq.message.RMqMessage;
import com.lx.pl.constant.LockPrefixConstant;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.util.UUID;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.List;

import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.service.LoadBalanceService.USER_TASK_TIMESTAMP;

/**
 * Gemini服务类
 * 基于fal.ai nano-banana API
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GeminiService {

    @Autowired
    private GeminiApiClient geminiApiClient;

    @Autowired
    private GeminiConfig geminiConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private NormalMessageProducer normalMessageProducer;

    @Autowired
    private SendPollingService sendPollingService;

    @Autowired
    private LumenService lumenService;

    @Value("${rocketmq.image.process.tag}")
    private String mjImageProcessTag;
    @Value("${rocketmq.piclumen.topic}")
    private String mjImageProcessTopic;

    // Redis key常量
    private static final String GEMINI_TASK_LOCK = "gemini_task_";
    public static final String GEMINI_TASK_PREFIX = "gemini:task:";
    private static final String GEMINI_USER_CONCURRENT_PREFIX = "gemini:concurrent";
    private static final String GEMINI_IMG_PREFIX = "gemini:do_img:";

    /**
     * 创建Nano Banana图像生成任务
     *
     * @param prompt                  提示词
     * @param user                    用户
     * @param markId                  标记ID
     * @param fastHour                是否快速生图
     * @param platform                平台
     * @param genParameters           原始生图参数
     * @param feature                 功能类型
     * @param modelRightsVerifyResult 模型权益验证结果
     * @return 任务响应
     */
    public GeminiResponse.SubmitTaskResponse createNanoBananaTask(String prompt, User user,
                                                                  String markId, Boolean fastHour, String platform,
                                                                  GenGenericPara genParameters, String feature,
                                                                  ModelRightsVerifyResult modelRightsVerifyResult) {
        try {
            // 检查是否为图生图模式
            if (genParameters.getMulti_img2img_info() != null &&
                    !CollectionUtils.isEmpty(genParameters.getMulti_img2img_info().getStyle_list())) {
                // 图生图模式：使用图片编辑API
                return createMultiImageEditTask(prompt, user, markId, fastHour, platform, genParameters, feature, modelRightsVerifyResult);
            }

            // 构建请求
            GeminiRequest.NanoBananaRequest request = new GeminiRequest.NanoBananaRequest();
            //在提示词最后增加生图比例
//            if (genParameters.getResolution() != null) {
//                // 计算宽高比
//                String aspectRatio = AspectRatioUtils.getAspectRatioLabel(genParameters.getResolution().getWidth(), genParameters.getResolution().getHeight());
//                request.setPrompt("Aspect ratio (" + aspectRatio + "). " + prompt);
//                String aspectRatio1 = AspectRatioUtils.getAspectRatio(genParameters.getResolution().getWidth(), genParameters.getResolution().getHeight());
//                request.setPrompt("required resolution: (" + aspectRatio1 + "). " + prompt);
//            }
            request.setPrompt(genParameters.getPrompt());
            request.setOutputFormat(geminiConfig.getDefaultOutputFormat());
            request.setNumImages(genParameters.getResolution().getBatch_size());
            request.setSyncMode(geminiConfig.getSyncModeEnabled());

            log.info("Creating Gemini Nano Banana task for user: {}, prompt: {}",
                    user.getLoginName(), prompt);

            // 调用API
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.SubmitTaskResponse> response = geminiApiClient
                    .submitNanoBananaTask(authHeader, request)
                    .execute();

            if (!response.isSuccessful()) {
                GeminiApiException geminiException = GeminiErrorParser.parseError(response);
                log.error("Gemini API call failed: {}", GeminiErrorParser.buildDetailedErrorLog(geminiException));

                // 根据错误类型抛出相应的LogicException
                throw new LogicException(geminiException.getLogicErrorCode());
            }

            GeminiResponse.SubmitTaskResponse responseData = response.body();
            if (responseData == null || StringUtil.isBlank(responseData.getRequestId())) {
                log.error("Gemini API returned null response or empty request ID");
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "Gemini API返回空响应");
            }

            // 添加到并发任务列表
            String userConcurrentKey = GEMINI_USER_CONCURRENT_PREFIX;
            redisService.putDataToHash(userConcurrentKey, responseData.getRequestId(), System.currentTimeMillis());

            // 如果提供了markId等参数，则保存PromptRecord到数据库
            if (StringUtil.isNotBlank(markId)) {
                savePromptRecord(responseData.getRequestId(), prompt, user, markId, fastHour, platform, genParameters, feature, modelRightsVerifyResult);

                // 保存Gemini任务状态到Redis
                saveGeminiTaskStatusToRedis(responseData.getRequestId(), markId, user.getLoginName());

                // 启动任务状态轮询
                startTaskStatusPolling(responseData.getRequestId(), user.getLoginName(), geminiConfig.getFirstDelaySeconds());
            }

            log.info("Gemini Nano Banana task created successfully, requestId: {}", responseData.getRequestId());
            return responseData;

        } catch (GeminiApiException e) {
            // 重新抛出已解析的Gemini API异常
            throw new LogicException(e.getLogicErrorCode());
        } catch (IOException e) {
            log.error("Gemini API call failed with IOException", e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "网络连接失败，请检查网络后重试");
        } catch (Exception e) {
            log.error("Unexpected error during Gemini task creation", e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "创建Gemini任务时发生未知错误，请稍后重试");
        }
    }

    /**
     * 创建Gemini图片编辑任务
     *
     * @param prompt                  提示词
     * @param user                    用户
     * @param markId                  标记ID
     * @param fastHour                是否快速生图
     * @param platform                平台
     * @param genParameters           原始生图参数
     * @param feature                 功能类型
     * @param modelRightsVerifyResult 模型权益验证结果
     * @return 任务响应
     */
    public GeminiResponse.SubmitTaskResponse createImageEditTask(String prompt, User user,
                                                                 String markId, Boolean fastHour, String platform,
                                                                 GenGenericPara genParameters, String feature,
                                                                 ModelRightsVerifyResult modelRightsVerifyResult) {
        try {
            // 构建图片编辑请求
            GeminiRequest.ImageEditRequest request = new GeminiRequest.ImageEditRequest();
            request.setPrompt(genParameters.getPrompt());
            request.setOutputFormat(geminiConfig.getDefaultOutputFormat());
            request.setSyncMode(geminiConfig.getSyncModeEnabled());

            // 获取输入图像URL（从multi_img2img_info中获取第一张图片）
            if (genParameters.getMulti_img2img_info() != null &&
                    !CollectionUtils.isEmpty(genParameters.getMulti_img2img_info().getStyle_list())) {
                String imageUrl = genParameters.getMulti_img2img_info().getStyle_list().get(0).getImg_url();
                if (StringUtil.isBlank(imageUrl)) {
                    throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "图片URL不能为空");
                }
                request.setImageUrl(imageUrl);

                // 设置编辑强度（如果有权重参数）
                Double weight = genParameters.getMulti_img2img_info().getStyle_list().get(0).getWeight();
                if (weight != null && weight > 0) {
                    request.setStrength(Math.min(weight, 1.0)); // 确保不超过1.0
                }
            } else {
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "图生图参数不能为空");
            }

            log.info("Creating Gemini Image Edit task for user: {}, prompt: {}, imageUrl: {}",
                    user.getLoginName(), prompt, request.getImageUrl());

            // 调用API
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.SubmitTaskResponse> response = geminiApiClient
                    .submitImageEditTask(authHeader, request)
                    .execute();

            if (!response.isSuccessful()) {
                GeminiApiException geminiException = GeminiErrorParser.parseError(response);
                log.error("Gemini Image Edit API call failed: {}", GeminiErrorParser.buildDetailedErrorLog(geminiException));

                // 根据错误类型抛出相应的LogicException
                throw new LogicException(geminiException.getLogicErrorCode());
            }

            GeminiResponse.SubmitTaskResponse responseData = response.body();
            if (responseData == null || StringUtil.isBlank(responseData.getRequestId())) {
                log.error("Gemini Image Edit API returned null response or empty request ID");
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "Gemini图片编辑API返回空响应");
            }

            // 添加到并发任务列表
            String userConcurrentKey = GEMINI_USER_CONCURRENT_PREFIX;
            redisService.putDataToHash(userConcurrentKey, responseData.getRequestId(), System.currentTimeMillis());

            // 如果提供了markId等参数，则保存PromptRecord到数据库
            if (StringUtil.isNotBlank(markId)) {
                savePromptRecord(responseData.getRequestId(), prompt, user, markId, fastHour, platform, genParameters, feature, modelRightsVerifyResult);

                // 保存Gemini任务状态到Redis
                saveGeminiTaskStatusToRedis(responseData.getRequestId(), markId, user.getLoginName());

                // 启动任务状态轮询
                startTaskStatusPolling(responseData.getRequestId(), user.getLoginName(), geminiConfig.getFirstDelaySeconds());
            }

            log.info("Gemini Image Edit task created successfully, requestId: {}", responseData.getRequestId());
            return responseData;

        } catch (GeminiApiException e) {
            // 重新抛出已解析的Gemini API异常
            throw new LogicException(e.getLogicErrorCode());
        } catch (IOException e) {
            log.error("Gemini Image Edit API call failed with IOException", e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "网络连接失败，请检查网络后重试");
        } catch (Exception e) {
            log.error("Unexpected error during Gemini Image Edit task creation", e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "创建Gemini图片编辑任务时发生未知错误，请稍后重试");
        }
    }

    /**
     * 创建多图片编辑任务
     *
     * @param prompt                  提示词
     * @param user                    用户
     * @param markId                  标记ID
     * @param fastHour                是否快速生图
     * @param platform                平台
     * @param genParameters           原始生图参数
     * @param feature                 功能类型
     * @param modelRightsVerifyResult 模型权益验证结果
     * @return 任务响应
     */
    public GeminiResponse.SubmitTaskResponse createMultiImageEditTask(String prompt, User user,
                                                                      String markId, Boolean fastHour, String platform,
                                                                      GenGenericPara genParameters, String feature,
                                                                      ModelRightsVerifyResult modelRightsVerifyResult) {
        try {
            // 验证多图片编辑参数
            if (genParameters.getMultiImgEditPara() == null || genParameters.getMultiImgEditPara().isEmpty()) {
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "多图片编辑参数不能为空");
            }

            // 使用第一张图片进行编辑（Gemini API目前只支持单张图片编辑）
            GenGenericPara.ImgEditPara firstImage = genParameters.getMultiImgEditPara().get(0);
            if (StringUtil.isBlank(firstImage.getImgUrl())) {
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "图片URL不能为空");
            }

            // 构建图片编辑请求
            GeminiRequest.ImageEditRequest request = new GeminiRequest.ImageEditRequest();
            request.setPrompt(genParameters.getPrompt());
            request.setImageUrl(firstImage.getImgUrl());
            request.setOutputFormat(geminiConfig.getDefaultOutputFormat());
            request.setSyncMode(geminiConfig.getSyncModeEnabled());

            log.info("Creating Gemini Multi Image Edit task for user: {}, prompt: {}, imageUrl: {}",
                    user.getLoginName(), prompt, request.getImageUrl());

            // 调用API
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.SubmitTaskResponse> response = geminiApiClient
                    .submitImageEditTask(authHeader, request)
                    .execute();

            if (!response.isSuccessful()) {
                GeminiApiException geminiException = GeminiErrorParser.parseError(response);
                log.error("Gemini Multi Image Edit API call failed: {}", GeminiErrorParser.buildDetailedErrorLog(geminiException));

                // 根据错误类型抛出相应的LogicException
                throw new LogicException(geminiException.getLogicErrorCode());
            }

            GeminiResponse.SubmitTaskResponse responseData = response.body();
            if (responseData == null || StringUtil.isBlank(responseData.getRequestId())) {
                log.error("Gemini Multi Image Edit API returned null response or empty request ID");
                throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "Gemini多图片编辑API返回空响应");
            }

            // 添加到并发任务列表
            String userConcurrentKey = GEMINI_USER_CONCURRENT_PREFIX;
            redisService.putDataToHash(userConcurrentKey, responseData.getRequestId(), System.currentTimeMillis());

            // 如果提供了markId等参数，则保存PromptRecord到数据库
            if (StringUtil.isNotBlank(markId)) {
                savePromptRecord(responseData.getRequestId(), prompt, user, markId, fastHour, platform, genParameters, feature, modelRightsVerifyResult);

                // 保存Gemini任务状态到Redis
                saveGeminiTaskStatusToRedis(responseData.getRequestId(), markId, user.getLoginName());

                // 启动任务状态轮询
                startTaskStatusPolling(responseData.getRequestId(), user.getLoginName(), geminiConfig.getFirstDelaySeconds());
            }

            log.info("Gemini Multi Image Edit task created successfully, requestId: {}", responseData.getRequestId());
            return responseData;

        } catch (GeminiApiException e) {
            // 重新抛出已解析的Gemini API异常
            throw new LogicException(e.getLogicErrorCode());
        } catch (IOException e) {
            log.error("Gemini Multi Image Edit API call failed with IOException", e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "网络连接失败，请检查网络后重试");
        } catch (Exception e) {
            log.error("Unexpected error during Gemini Multi Image Edit task creation", e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "创建Gemini多图片编辑任务时发生未知错误，请稍后重试");
        }
    }

    /**
     * 检查Gemini并发任务数限制
     */
    public boolean checkGeminiConcurrentJobs(User user, String requestId) {
        String lockKey = LockPrefixConstant.CONCURRENT_EXECUTION_LOCK_PREFIX + "gemini";
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();

            // 获取用户当前的Gemini任务列表
            String userConcurrentKey = GEMINI_USER_CONCURRENT_PREFIX;
            List<String> userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 清理已过期的任务
            cleanExpiredGeminiTasks(userConcurrentKey, userTaskList);

            // 重新获取清理后的任务列表
            userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 检查是否超过最大并发数限制
            int maxConcurrentJobs = geminiConfig.getMaxConcurrentJobs() != null ?
                    geminiConfig.getMaxConcurrentJobs() : 999;

            if (userTaskList.size() >= maxConcurrentJobs) {
                log.warn("User {} Gemini concurrent jobs limit exceeded: {}/{}",
                        user.getLoginName(), userTaskList.size(), maxConcurrentJobs);
                return true; // 超过限制
            }

            return false; // 未超过限制

        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 清理已过期的Gemini任务
     */
    private void cleanExpiredGeminiTasks(String userConcurrentKey, List<String> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        for (String jobId : taskList) {
            String taskKey = GEMINI_TASK_PREFIX + jobId;
            if (!redisService.hasKey(taskKey)) {
                // 任务已过期，从并发列表中移除
                redisService.deleteFieldFromHash(userConcurrentKey, jobId);
                log.debug("Removed expired Midjourney task: {}", jobId);
            }
        }

//        long currentTime = System.currentTimeMillis();
//        long expireTime = 2 * 60 * 60 * 1000; // 2小时过期
//
//        for (String taskId : taskList) {
//            try {
//                Object timestamp = redisService.getDataFromHash(userConcurrentKey, taskId);
//                if (timestamp != null) {
//                    long taskTime = Long.parseLong(timestamp.toString());
//                    if (currentTime - taskTime > expireTime) {
//                        redisService.deleteFieldFromHash(userConcurrentKey, taskId);
//                        log.debug("Cleaned expired Gemini task: {}", taskId);
//                    }
//                }
//            } catch (Exception e) {
//                log.warn("Clean expired Gemini task error: {}", taskId, e);
//                redisService.deleteFieldFromHash(userConcurrentKey, taskId);
//            }
//        }
    }

    /**
     * 保存PromptRecord到数据库
     */
    private void savePromptRecord(String requestId, String prompt, User user, String markId, Boolean fastHour,
                                  String platform, GenGenericPara genParameters, String feature,
                                  ModelRightsVerifyResult modelRightsVerifyResult) {
        try {
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setPromptId(requestId);
            promptRecord.setMarkId(markId);
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setPrompt(prompt);
            promptRecord.setNegativePrompt(genParameters.getNegative_prompt());
            promptRecord.setModelId(genParameters.getModel_id());
            promptRecord.setBatchSize(genParameters.getResolution().getBatch_size());
            promptRecord.setFastHour(fastHour);
            promptRecord.setPlatform(platform);
            promptRecord.setFeatureName(feature);
            promptRecord.setCreateTime(LocalDateTime.now());
            promptRecord.setOriginCreate("create");

            // 设置点数消耗
            if (modelRightsVerifyResult != null) {
                promptRecord.setCostLumens(modelRightsVerifyResult.getCostLumen());
            }
            promptRecord.setGenStartTime(LocalDateTime.now());
            if (FeaturesType.ttp.getValue().equals(feature)) {
                promptRecord.setOriginCreate(OriginCreate.create.getValue());
            } else if (FeaturesType.edit.getValue().equals(feature)) {
                promptRecord.setOriginCreate(OriginCreate.edit.getValue());
            } else if (FeaturesType.ptpGemini.getValue().equals(feature)) {
                promptRecord.setOriginCreate(OriginCreate.picCreate.getValue());
            }
            // 如果有原始参数，保存为genInfo
            if (genParameters != null) {
                promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
            }

            // 设置宽高比，根据前端SHAPE_ALL配置处理
            String aspectRatio = "1024 x 1024"; // 默认值
            if (genParameters instanceof GenGenericPara) {
                GenGenericPara genPara = (GenGenericPara) genParameters;
                aspectRatio = AspectRatioUtils.getAspectRatio(genPara);
            }
            promptRecord.setAspectRatio(aspectRatio);
            promptRecord.setFeatureName(feature);
            promptRecord.setFastHour(fastHour != null ? fastHour : true);
            promptRecord.setPlatform(platform);
            promptRecord.setCostLumens(modelRightsVerifyResult.getCostLumen());
            promptRecord.setUseFreeTrial(modelRightsVerifyResult.isUseFreeTrial());
            promptRecord.setUsePayTrial(modelRightsVerifyResult.isUsePayTrial());

            promptRecordMapper.insert(promptRecord);
            log.info("Saved PromptRecord for Gemini task: {}", requestId);

        } catch (Exception e) {
            log.error("Failed to save PromptRecord for Gemini task: {}", requestId, e);
        }
    }

    /**
     * 保存Gemini任务状态到Redis
     */
    private void saveGeminiTaskStatusToRedis(String taskId, String markId, String loginName) {
        try {
            // 1. 保存taskId -> markId的映射
            redisService.stringSet(taskId, markId, 2, TimeUnit.HOURS);

            // 2. 保存markId -> loginName的映射
            redisService.stringSet(markId, loginName, 2, TimeUnit.HOURS);

            // 3. 在用户hash中设置任务状态为0（直接进入开始执行状态）
            redisService.putDataToHash(loginName, markId, 0, 2, TimeUnit.HOURS);

            // 4. 设置任务时间戳
            redisService.set(USER_TASK_TIMESTAMP + markId, System.currentTimeMillis(), 2, TimeUnit.HOURS);

            String taskKey = GEMINI_TASK_PREFIX + taskId;
            redisService.stringSet(taskKey, markId, 10, TimeUnit.MINUTES);

            // 预扣
            lumenService.notFinishTask(loginName);

            log.info("Saved Gemini task status to Redis: {}", taskId);

        } catch (Exception e) {
            log.error("Failed to save Gemini task status to Redis: {}", taskId, e);
        }
    }

    /**
     * 启动任务状态轮询（使用统一轮询服务）
     */
    private void startTaskStatusPolling(String requestId, String loginName, Integer delaySeconds) {
        log.info("Starting Gemini task status polling for requestId: {}, user: {}", requestId, loginName);

        int maxAttempts = geminiConfig.getMaxPollingAttempts() != null ?
                geminiConfig.getMaxPollingAttempts() : 20;
        int pollingInterval = geminiConfig.getPollingIntervalSeconds() != null ?
                geminiConfig.getPollingIntervalSeconds() * 1000 : 3000; // 转换为毫秒

        // 创建轮询消息
        TaskPollingVo pollingVo = new TaskPollingVo(
                TaskTypeForMq.GEMINI.getType(),
                requestId,
                loginName,
                0, // 初始轮询次数
                maxAttempts,
                pollingInterval,
                System.currentTimeMillis(),
                null // Gemini不需要pollingUrl
        );

        try {
            // 发送延迟消息到统一轮询服务
            sendPollingService.sendPollingMessage(pollingVo, delaySeconds);
            log.info("Sent Gemini polling message for requestId: {}", requestId);

        } catch (Exception e) {
            log.error("Failed to send Gemini polling message for requestId: {}", requestId, e);
        }
    }

    /**
     * 获取任务结果（根据任务类型自动选择API端点）
     */
    public GeminiResponse.FalApiResult getTaskResult(String requestId, String loginName) {
        try {
            // 查询数据库获取任务的feature类型
            LambdaQueryWrapper<PromptRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PromptRecord::getPromptId, requestId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .select(PromptRecord::getFeatureName);

            PromptRecord promptRecord = promptRecordMapper.selectOne(queryWrapper);

            // 根据feature类型选择合适的API端点
            if (promptRecord != null &&
                    (FeaturesType.edit.getValue().equals(promptRecord.getFeatureName()) ||
                            FeaturesType.ptpGemini.getValue().equals(promptRecord.getFeatureName()))) {
                // 图片编辑或图生图任务，使用图片编辑API端点
                return getImageEditTaskResult(requestId, loginName);
            } else {
                // 文生图任务，使用原始API端点
                return getNanoBananaTaskResult(requestId, loginName);
            }

        } catch (Exception e) {
            log.error("Error determining task type for requestId: {}, falling back to nano-banana API", requestId, e);
            // 出错时回退到原始API
            return getNanoBananaTaskResult(requestId, loginName);
        }
    }

    /**
     * 获取Nano Banana任务结果
     */
    private GeminiResponse.FalApiResult getNanoBananaTaskResult(String requestId, String loginName) {
        try {
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.FalApiResult> response = geminiApiClient
                    .getTaskResult(authHeader, requestId)
                    .execute();

            if (!response.isSuccessful()) {
                GeminiApiException geminiException = GeminiErrorParser.parseError(response);
                log.error("Failed to get Gemini Nano Banana task result: {}", GeminiErrorParser.buildDetailedErrorLog(geminiException));
                handleTaskFailure(requestId, loginName, geminiException.getLogicErrorCode().getMessage());
                throw new LogicException(geminiException.getLogicErrorCode());
            }

            return response.body();

        } catch (IOException e) {
            log.error("IOException while getting Gemini Nano Banana task result: {}", requestId, e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "网络连接失败，请稍后重试");
        }
    }

    /**
     * 获取任务状态（根据任务类型自动选择API端点）
     */
    public GeminiResponse.TaskStatusResponse getTaskStatus(String requestId, String loginName) {
        try {
            // 查询数据库获取任务的feature类型
            LambdaQueryWrapper<PromptRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PromptRecord::getPromptId, requestId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .select(PromptRecord::getFeatureName);

            PromptRecord promptRecord = promptRecordMapper.selectOne(queryWrapper);

            // 根据feature类型选择合适的API端点
            if (promptRecord != null &&
                    (FeaturesType.edit.getValue().equals(promptRecord.getFeatureName()) ||
                            FeaturesType.ptpGemini.getValue().equals(promptRecord.getFeatureName()))) {
                // 图片编辑或图生图任务，使用图片编辑API端点
                return getImageEditTaskStatus(requestId, loginName);
            } else {
                // 文生图任务，使用原始API端点
                return getNanoBananaTaskStatus(requestId, loginName);
            }

        } catch (Exception e) {
            log.error("Error determining task type for requestId: {}, falling back to nano-banana API", requestId, e);
            // 出错时回退到原始API
            return getNanoBananaTaskStatus(requestId, loginName);
        }
    }

    /**
     * 获取Nano Banana任务状态
     */
    private GeminiResponse.TaskStatusResponse getNanoBananaTaskStatus(String requestId, String loginName) {
        try {
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.TaskStatusResponse> response = geminiApiClient
                    .getTaskStatus(authHeader, requestId)
                    .execute();

            if (!response.isSuccessful()) {
                GeminiApiException geminiException = GeminiErrorParser.parseError(response);
                log.error("Failed to get Gemini Nano Banana task status: {}", GeminiErrorParser.buildDetailedErrorLog(geminiException));
                handleTaskFailure(requestId, loginName, geminiException.getLogicErrorCode().getMessage());
                throw new LogicException(geminiException.getLogicErrorCode());
            }

            return response.body();

        } catch (IOException e) {
            log.error("IOException while getting Gemini Nano Banana task status: {}", requestId, e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "网络连接失败，请稍后重试");
        }
    }

    /**
     * 获取图片编辑任务状态
     */
    public GeminiResponse.TaskStatusResponse getImageEditTaskStatus(String requestId, String loginName) {
        try {
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.TaskStatusResponse> response = geminiApiClient
                    .getImageEditTaskStatus(authHeader, requestId)
                    .execute();

            if (!response.isSuccessful()) {
                GeminiApiException geminiException = GeminiErrorParser.parseError(response);
                log.error("Failed to get Gemini Image Edit task status: {}", GeminiErrorParser.buildDetailedErrorLog(geminiException));
                handleTaskFailure(requestId, loginName, geminiException.getLogicErrorCode().getMessage());
                throw new LogicException(geminiException.getLogicErrorCode());
            }

            return response.body();

        } catch (IOException e) {
            log.error("IOException while getting Gemini Image Edit task status: {}", requestId, e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "网络连接失败，请稍后重试");
        }
    }

    /**
     * 获取图片编辑任务结果
     */
    public GeminiResponse.FalApiResult getImageEditTaskResult(String requestId, String loginName) {
        try {
            String authHeader = "Key " + geminiConfig.getApiKey();
            Response<GeminiResponse.FalApiResult> response = geminiApiClient
                    .getImageEditTaskResult(authHeader, requestId)
                    .execute();

            if (!response.isSuccessful()) {
                GeminiApiException geminiException = GeminiErrorParser.parseError(response);
                log.error("Failed to get Gemini Image Edit task result: {}", GeminiErrorParser.buildDetailedErrorLog(geminiException));
                handleTaskFailure(requestId, loginName, geminiException.getLogicErrorCode().getMessage());
                throw new LogicException(geminiException.getLogicErrorCode());
            }

            return response.body();

        } catch (IOException e) {
            log.error("IOException while getting Gemini Image Edit task result: {}", requestId, e);
            throw new LogicException(LogicErrorCode.GEMINI_API_ERROR, "网络连接失败，请稍后重试");
        }
    }

    /**
     * 处理任务成功
     */
    public void handleTaskSuccess(String taskType, String requestId, String loginName) {
        //分布式锁
        String lockKey = GEMINI_TASK_LOCK + requestId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();
            //图片处理锁-防止重复处理
            String taskKey = GEMINI_IMG_PREFIX + requestId;
            String s = redisService.stringGet(taskKey);
            if (StringUtil.isNotBlank(s)) {
                return;
            }

            redisService.stringSet(taskKey, requestId, 5, TimeUnit.MINUTES);

            if (promptRecordFinished(requestId, loginName)) {
                return;
            }

            // 获取任务结果
            GeminiResponse.FalApiResult taskResult = getTaskResult(requestId, loginName);
            if (taskResult == null) {
                log.warn("Task result is null for successful requestId: {}", requestId);
                return;
            }

            // 发送图片处理MQ消息（使用统一的图片处理服务）
            if (!CollectionUtils.isEmpty(taskResult.getImages())) {
                sendImageProcessMessage(taskType, requestId, loginName, taskResult);
            }

            log.info("Successfully processed Gemini task completion for requestId: {}", requestId);

        } finally {
            lock.unlock();
        }
    }

    /**
     * 处理任务失败
     */
    public void handleTaskFailure(String requestId, String loginName, String failureMessage) {
        //分布式锁
        String lockKey = GEMINI_TASK_LOCK + requestId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();
            // 更新任务状态为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, requestId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getFailureMessage, failureMessage != null ? failureMessage : "Task failed")
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(requestId, loginName);

            log.info("Processed failed Gemini task for requestId: {}", requestId);

        } finally {
            lock.unlock();
        }
    }

    /**
     * 处理任务超时
     */
    public void handleTaskTimeout(String requestId, String loginName) {
        try {
            // 将超时任务标记为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, requestId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getFailureMessage, "Task timeout")
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(requestId, loginName);

            log.warn("Processed timeout Gemini task for requestId: {}", requestId);

        } catch (Exception e) {
            log.error("Error handling task timeout for requestId: " + requestId, e);
        }
    }

    /**
     * 清理任务从Redis
     */
    public void cleanupTaskFromRedis(String requestId, String loginName) {
        try {
            // 获取markId
            String markId = redisService.stringGet(requestId);

            // 清理Gemini任务状态（模拟传统任务的清理方式）
            if (StringUtil.isNotBlank(markId)) {
                // 从用户hash中删除任务状态(重要，删除之后，前端轮询会直接认定为任务完成进行最终处理然后删除redisService.delete(originPromptRecord.getMarkId())，不需要-2状态)
                redisService.deleteFieldFromHash(loginName, markId);

                // 删除markId -> loginName映射，先别删除，轮询时候再去删除
//                redisService.delete(markId);

                // 删除任务时间戳
                redisService.delete(USER_TASK_TIMESTAMP + markId);

                log.debug("Cleaned up Gemini task status for markId: {}", markId);
            }

            // 删除requestId -> markId映射
            redisService.delete(requestId);

            // 删除任务信息
            String taskKey = GEMINI_TASK_PREFIX + requestId;
            redisService.delete(taskKey);


            // 删除图片处理锁-防止重复处理
            String imgKey = GEMINI_IMG_PREFIX + requestId;
            redisService.delete(imgKey);

            // 从并发任务列表中删除
            removeGeminiConcurrentJob(loginName, requestId);

            // 刷新预扣任务
            lumenService.notFinishTask(loginName);
            log.info("Cleaned up all Redis data for requestId: {}, markId: {}", requestId, markId);

        } catch (Exception e) {
            log.error("Error cleaning up task from redis for requestId: " + requestId, e);
        }
    }

    /**
     * 检查任务是否已完成
     */
    private boolean promptRecordFinished(String requestId, String loginName) {
        try {
            PromptRecord promptRecord = promptRecordMapper.selectOne(
                    new LambdaQueryWrapper<PromptRecord>()
                            .eq(PromptRecord::getPromptId, requestId)
                            .eq(PromptRecord::getLoginName, loginName)
            );

            return promptRecord != null && promptRecord.getGenEndTime() != null;

        } catch (Exception e) {
            log.error("Error checking if task finished for requestId: " + requestId, e);
            return false;
        }
    }

    /**
     * 发送图片处理消息
     */
    private void sendImageProcessMessage(String taskType, String jobId, String loginName, GeminiResponse.FalApiResult taskData) {
        try {
            String markId = redisService.stringGet(jobId);
            List<GeminiResponse.ImageInfo> images = taskData.getImages();

            // 构建图片处理信息列表
            List<MjImageProcessVo.ImageInfo> imageInfos = new ArrayList<>();
            for (GeminiResponse.ImageInfo resultImage : images) {
                MjImageProcessVo.ImageInfo imageInfo = new MjImageProcessVo.ImageInfo();
                imageInfo.setOriginalUrl(resultImage.getUrl());
                imageInfo.setFileName(extractFileNameFromUrl(resultImage.getUrl()));

                // 默认Midjourney图片尺寸
                imageInfo.setWidth(1024);
                imageInfo.setHeight(1024);

                // 注意：这里不设置promptFileId，因为PromptFile还没有插入数据库
                // 图片处理服务需要根据jobId和imageUrl来查找对应的PromptFile记录
                imageInfos.add(imageInfo);
            }

            MjImageProcessVo processVo = new MjImageProcessVo();
            processVo.setTaskType(taskType);
            processVo.setJobId(jobId);
            processVo.setLoginName(loginName);
            processVo.setMarkId(markId);
            processVo.setImageInfos(imageInfos);
            processVo.setNsfwCheck(false);

            CommonMqMessage<MjImageProcessVo> mqMessage = new RMqMessage<>(
                    mjImageProcessTopic,
                    mjImageProcessTag,
                    jobId + "_image_process"
            );
            mqMessage.setMessage(processVo);

            normalMessageProducer.syncSend(mqMessage);

            log.info("Sent image process message for jobId: {}, imageCount: {}", jobId, imageInfos.size());
        } catch (Exception e) {
            log.error("Failed to send image process message for jobId: {}", jobId, e);
        }
    }

    /**
     * 移除用户的Gemini并发任务
     */
    public void removeGeminiConcurrentJob(String loginName, String requestId) {
        try {
            String userConcurrentKey = GEMINI_USER_CONCURRENT_PREFIX;
            redisService.deleteFieldFromHash(userConcurrentKey, requestId);
            log.debug("Removed Gemini concurrent job: {} for user: {}", requestId, loginName);
        } catch (Exception e) {
            log.error("Remove Gemini concurrent job error", e);
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String imageUrl) {
        if (StringUtil.isBlank(imageUrl)) {
            return "gemini_image.png";
        }

        try {
            String[] parts = imageUrl.split("/");
            String lastPart = parts[parts.length - 1];

            // 移除查询参数
            if (lastPart.contains("?")) {
                lastPart = lastPart.substring(0, lastPart.indexOf("?"));
            }

            return StringUtil.isNotBlank(lastPart) ? lastPart : "gemini_image.png";
        } catch (Exception e) {
            log.warn("Failed to extract filename from URL: {}", imageUrl);
            return "gemini_image.png";
        }
    }
}
