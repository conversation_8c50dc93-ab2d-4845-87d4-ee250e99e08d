package com.lx.pl.pay.stripe.service.strategy.invoice;

import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripeInvoice;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.service.StripeInvoiceService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.stripe.model.Invoice;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static com.lx.pl.pay.PayConstant.INVOICE_LOCK_PREFIX;

/**
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = "invoice.payment_failed")
public class InvoicePaymentFailedEvent extends IStripeEventHandler<Invoice> {

    @Resource
    private StripeInvoiceService stripeInvoiceService;

    @Override
    public void handleEvent(Invoice event, String eventId) {
        String id = event.getId();
        RLock lock = redissonClient.getLock(INVOICE_LOCK_PREFIX + id);
        log.info("start invoice.payment_failed: {} {} {}", eventId, System.currentTimeMillis(), INVOICE_LOCK_PREFIX + id);
        try {
            lock.lock();
            applicationContext.getBean(InvoicePaymentFailedEvent.class)
                    .doHandleEvent(event, id);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock invoice.payment_failed: {} {} {}", eventId, System.currentTimeMillis(), INVOICE_LOCK_PREFIX + id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doHandleEvent(Invoice event, String id) {
        log.info("start doHandleEvent invoice.payment_failed : {} customer: {}", event.getId(), event.getCustomer());
        StripeInvoice one = stripeInvoiceService.lambdaQuery()
                .eq(StripeInvoice::getInvoiceId, id)
                .one();
        if (one == null) {
            StripeUserCustomer userCustomer = stripeUserCustomerService.lambdaQuery()
                    .eq(StripeUserCustomer::getCustomerId, event.getCustomer())
                    .one();
            StripeInvoice invoice = buildStripeInvoice(event, userCustomer);
            stripeInvoiceService.save(invoice);
            // 订单完成的处理逻辑
            log.info("save invoice.payment_failed : {} customer: {}", event.getId(), event.getCustomer());
        } else {
            if (InvoiceUtil.isTerminalState(one)) {
                log.info("isTerminalState invoice.payment_failed: {} {} {}", event.getCustomer(), event.getId(), event.getStatus());
                return;
            }
            stripeInvoiceService.lambdaUpdate()
                    .eq(StripeInvoice::getInvoiceId, id)
                    .set(StripeInvoice::getStatus, event.getStatus())
                    .set(StripeInvoice::getUpdateTime, LocalDateTime.now())
                    .set(event.getCustomerAddress()!=null, StripeInvoice::getCustomerCountry, event.getCustomerAddress().getCountry())
                    .update();
        }
    }


    @NotNull
    static StripeInvoice buildStripeInvoice(Invoice event, StripeUserCustomer userCustomer) {
        StripeInvoice invoice = new StripeInvoice();
        invoice.setInvoiceId(event.getId());
        invoice.setCustomerId(event.getCustomer());
        invoice.setAmountDue(event.getAmountDue());
        invoice.setAmountRemaining(event.getAmountRemaining());
        invoice.setAmountPaid(event.getAmountPaid());
        if (userCustomer != null) {
            invoice.setUserId(userCustomer.getUserId());
            invoice.setLoginName(userCustomer.getLoginName());
        }
        invoice.setBillingReason(event.getBillingReason());
        invoice.setCurrency(event.getCurrency());
        invoice.setHostedInvoiceUrl(event.getHostedInvoiceUrl());
        invoice.setInvoicePdf(event.getInvoicePdf());

        invoice.setSubscriptionId(event.getSubscription());
        invoice.setPaymentIntentId(event.getPaymentIntent());
        invoice.setStatus(event.getStatus());
        invoice.setCreateTime(LocalDateTime.now());

        invoice.setTotal(event.getTotal());
        invoice.setTotalExcludingTax(event.getTotalExcludingTax());
        if (event.getCustomerAddress() != null) {
            invoice.setCustomerCountry(event.getCustomerAddress().getCountry());
        }
        return invoice;
    }

}