package com.lx.pl.pay.stripe.service.strategy.customer;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.lx.pl.pay.stripe.service.PayLogicPurchaseRecordService;
import com.lx.pl.pay.stripe.service.StripeSubscriptionLogService;
import com.lx.pl.pay.stripe.service.StripeSubscriptionRecordService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.lx.pl.service.CommMessageService;
import com.lx.pl.service.StripeProductService;
import com.lx.pl.service.UserService;
import com.stripe.model.Subscription;
import com.stripe.model.SubscriptionItem;
import com.stripe.model.SubscriptionItemCollection;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static com.lx.pl.pay.PayConstant.SUBSCRIPTION_LOCK_PREFIX;
import static com.lx.pl.pay.stripe.service.strategy.customer.CustomerSubscriptionCreateEvent.buildSubscriptionCurrent;

/**
 * Occurs whenever a subscription changes
 * (e.g., switching from one plan to another, or changing the status from trial to active).
 *
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = "customer.subscription.updated")
public class CustomerSubscriptionUpdateEvent extends IStripeEventHandler<Subscription> {

    @Resource
    private StripeSubscriptionRecordService stripeSubscriptionRecordService;
    @Resource
    private PayLogicPurchaseRecordService payLogicPurchaseRecordService;
    @Resource
    private UserService userService;
    @Resource
    private StripeProductService stripeProductService;
    @Resource
    private StripeSubscriptionLogService stripeSubscriptionLogService;

    @Resource
    private CommMessageService commMessageService;

    @Override
    public void handleEvent(Subscription subscription, String eventId) {
        String id = subscription.getId();
        RLock lock = redissonClient.getLock(SUBSCRIPTION_LOCK_PREFIX + id);
        log.info("lock customer.subscription.updated: {} {} {}", subscription.getCustomer(), eventId, SUBSCRIPTION_LOCK_PREFIX + id);
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(CustomerSubscriptionUpdateEvent.class)
                    .doHandleLogic(subscription);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock customer.subscription.updated: {} {} {}", subscription.getCustomer(), eventId, SUBSCRIPTION_LOCK_PREFIX + id);
            }
            try {
                if (StrUtil.isNotBlank(loginName)) {
                    User user = userService.getByLoginName(loginName);
                    if (user != null) {
                        updateUserVipStatus(user.getId());
                    }
                }
            } catch (Exception e) {
                log.error("更新用户VIP等级2 失败: {}", e.getMessage(), e);
            }
        }
    }

    private void updateUserVipStatus(Long userId) {
        SubscriptionCurrent subscription = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(userId);
        log.info("更新用户VIP等级2 start: loginName={} newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
        userService.updateUserVipInfo(subscription, userId);
        log.info("更新用户VIP等级2 end: loginName={}, newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
    }

    @Transactional(rollbackFor = Exception.class)
    public String doHandleLogic(Subscription event) {
        log.info("start customer.subscription.updated: {} {}", event.getCustomer(), event.getId());
        stripeSubscriptionLogService.saveSubscriptionLog(event);
        StripeSubscriptionRecord one = stripeSubscriptionRecordService.lambdaQuery()
                .eq(StripeSubscriptionRecord::getSubscriptionId, event.getId()).one();
        boolean priceUpgrade = false;
        String newPriceId = null;
        if (one == null) {
            one = stripeSubscriptionRecordService.saveSubscription(event);
            // 订单完成的处理逻辑
            log.info("save customer.subscription.created: {} {}", event.getCustomer(), event.getId());
        } else {
            LambdaUpdateChainWrapper<StripeSubscriptionRecord> updateChainWrapper = stripeSubscriptionRecordService.lambdaUpdate()
                    .eq(StripeSubscriptionRecord::getId, one.getId())
                    .set(StripeSubscriptionRecord::getSubStatus, event.getStatus())
                    .set(StripeSubscriptionRecord::getCancelledAt, event.getCanceledAt())
                    .set(StripeSubscriptionRecord::getCurrentPeriodStart, event.getCurrentPeriodStart())
                    .set(StripeSubscriptionRecord::getCurrentPeriodEnd, event.getCurrentPeriodEnd())
                    .set(StripeSubscriptionRecord::getLogicPeriodStart, event.getCurrentPeriodStart())
                    .set(StripeSubscriptionRecord::getLogicPeriodEnd, event.getCurrentPeriodEnd())
                    .set(StripeSubscriptionRecord::getUpdateTime, LocalDateTime.now());
            Subscription.CancellationDetails cancellationDetails = event.getCancellationDetails();
            if (cancellationDetails != null) {
                updateChainWrapper.set(StripeSubscriptionRecord::getCancelReason, cancellationDetails.getReason());
            }
            updateChainWrapper.update();
            one = stripeSubscriptionRecordService.lambdaQuery()
                    .eq(StripeSubscriptionRecord::getSubscriptionId, event.getId()).one();
            log.info("update customer.subscription.updated: {} {} {} {}", event.getCustomer(), event.getId(), one.getCancelledAt(), event.getCancelAt());
            String priceId = one.getPriceId();
            SubscriptionItemCollection items = event.getItems();
            if (items != null) {
                List<SubscriptionItem> data = items.getData();
                if (data != null && !data.isEmpty()) {
                    SubscriptionItem subscriptionItem = data.get(0);
                    newPriceId = subscriptionItem.getPrice().getId();
                    // 降级采用的是取消原订阅，升级采用的是更换priceId 所以priceId不一样一定是升级
                    priceUpgrade = !priceId.equals(newPriceId);
                }
            }
            log.info("update customer.subscription.updated: {} {}", event.getCustomer(), event.getId());
        }
        if (one != null && "active".equals(one.getSubStatus())) {
            if (!priceUpgrade) {
                log.info("update customer.subscription.updated priceUpgrade: {} {} {} {}", priceUpgrade, event.getCustomer(), event.getId(), event.getCancelAt());
                return activeSub(event, one);
            } else {
                log.info("upgradeSub customer.subscription.updated priceUpgrade: {} {} {}", priceUpgrade, event.getCustomer(), event.getId());
                return upgradeSub(event, one, newPriceId);
            }
        }
        log.info("end customer.subscription.updated: {} {}", event.getCustomer(), event.getId());
        return null;
    }

    private String upgradeSub(Subscription event, StripeSubscriptionRecord one, String newPriceId) {
        log.info("start upgradeSub {} oldPriceId: {} newPriceId: {}", event.getCustomer(), one.getPriceId(), newPriceId);
        long nowTime = System.currentTimeMillis() / 1000;
        // 取消用户当月之后的lumen 以及购买记录
        payLogicPurchaseRecordService.cancelLastRecord(one.getPriceId(), one.getSubscriptionId(), nowTime);

        StripeProduct newProduct = stripeProductService.lambdaQuery().eq(StripeProduct::getStripePriceId, newPriceId)
                .one();
        //更新record priceId
        stripeSubscriptionRecordService.lambdaUpdate()
                .eq(StripeSubscriptionRecord::getId, one.getId())
                .set(StripeSubscriptionRecord::getPriceId, newPriceId)
                .set(StripeSubscriptionRecord::getSubInterval, newProduct.getPriceInterval())
                .update();
        one.setPriceId(newPriceId);
        one.setSubInterval(newProduct.getPriceInterval());
        // 更新用户购买记录
        StripeProduct stripeProduct = stripeProductService.lambdaQuery()
                .eq(StripeProduct::getStripePriceId, newPriceId).one();

        boolean successSaveLumen = payLogicPurchaseRecordService.saveLogicPurchaseRecordForUpgrade(one, event.getLatestInvoice(), stripeProduct);
        if (successSaveLumen) {

            subscriptionCurrentService.updateCurrentSub(stripeProduct, one, event.getDiscount());

            // todo 立即升级
            commMessageService.addUserUpgradeVipMessage(newProduct.getPlanLevel(),one.getCurrentPeriodEnd(),newProduct.getPriceInterval(),one.getUserId());

        }
        log.info("end upgradeSub {} newPriceId: {}", event.getCustomer(), newPriceId);
        return one.getLoginName();
    }

    private String activeSub(Subscription event, StripeSubscriptionRecord one) {
        StripeProduct stripeProduct = stripeProductService.lambdaQuery()
                .eq(StripeProduct::getStripePriceId, one.getPriceId()).one();
        // 计算并保存逻辑发放记录
        boolean successSaveLumen = payLogicPurchaseRecordService.saveLogicPurchaseRecord(one, event, stripeProduct, false);
        log.info("save logic purchase record customer.subscription.updated: {} {} {} {}", one.getCustomerId(), event.getId(), successSaveLumen, one.getCancelledAt());
        return updateCurrentAndVipInfo(event, one, stripeProduct, successSaveLumen);
    }

    private String updateCurrentAndVipInfo(Subscription event, StripeSubscriptionRecord one, StripeProduct stripeProduct, boolean successSaveLumen) {
        if (successSaveLumen) {

            SubscriptionCurrent subscriptionCurrent = buildSubscriptionCurrent(one, stripeProduct, false, event.getDiscount());
            subscriptionCurrentService.saveOrUpdateStripeSubscriptionCurrent(subscriptionCurrent);
            // todo 新购 或者 续订 或者 未来升降级
            // 发送会员变更站内信
            commMessageService.addUserUpgradeVipMessage(subscriptionCurrent.getPlanLevel(),subscriptionCurrent.getCurrentPeriodEnd(),subscriptionCurrent.getPriceInterval(),subscriptionCurrent.getUserId());
        } else {
            subscriptionCurrentService.updateAutoRenewStatus(one.getSubscriptionId(), one.getCancelledAt() == null ? 1 : 0);
        }
        log.info("update user vip info customer.subscription.updated: {} {} {}", event.getCustomer(), event.getId(), one.getCancelledAt());
        return one.getLoginName();
    }
}