package com.lx.pl.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class LogicConstants {

    /**
     * 限流国家
     */
    public static final String[] LIMIT_COUNTRIES = {"India", "Pakistan", "Indonesia", "Egypt"};

    public static final String UNKNOWN_IP = "未知IP";

    public static final String INNER_IP = "内网IP";

    public static final String RULE_LIST_KEY = "model_feature";
    public static final String FAIR_QUEUE_NAME_PREFIX = "fairQueue_";
    public static final String UNFAIR_QUEUE_NAME_PREFIX = "unfairQueue_";
    public static final String QUEUE_COUNTER_SUFFIX = "_counter";
    public static final String COMFY_SIZE_PREFIX = "comfy_size:";
    public static final String EXCLUDE_MG_FG = "m0f0";
    public static final String MARKID_SERVERID_LIST = "markId_serverId_list";
    public static final String LB_SYSTEM_PARA = "lb_system_para";
    public static final String FAIR_TASK_THRESHOLD = "fairTaskThreshold";   // 公平队列任务阈值
    public static final String UNFAIR_TASK_THRESHOLD = "unfairTaskThreshold"; // 非公平队列任务阈值
    public static final String PRELOADING_TASK_THRESHOLD = "preloadingTaskThreshold"; // 预载gpu队列任务阈值
    public static final String BLOB_FAIR_WAIT_TIME = "blobFairWaitTime"; // 阻塞等待时长，毫秒

    public static final String USER_REGISTER_LOCK_PREFIX = "user_register_"; // 用户注册锁前缀

    public static final String GPU_LOCK_PREFIX = "gpuLock_"; // gpu锁前缀

    public static final String IOS_VERSION = "Ios-Version"; //IOS版本号

    public static final String ANDROID_VERSION = "Android-Version"; //android版本号
    public static final String BL_RULE_FLUX = "blRuleFlux"; //拉黑flux实例key名
    public static final String BL_RULE_COMMON = "blRuleCommon"; //拉黑common实例key名
    public static final String BL_FLUX_QUEUE = "blFluxQueue"; //拉黑flux队列
    public static final String BL_COMMON_QUEUE = "blCommonQueue"; //拉黑common队列

    // 临时token key
    public static final String TMP_TOKEN_COUNT = "tmp_token_count";

    public static final String FAIL_MSG_SPLIT = "####";
    //儿童色情过滤总计数
    public static final String CHILD_PRON_COUNT = "child_pron_count";
    public static final String ADULT_PRON_COUNT = "adult_pron_count";
    public static final String BUY_PLAN_USER = "user:first:plan";
    // 定义生图数量与等待时间的映射关系
    public static final int[][] DELAY_TIMES = {
            {50, 15, 15},
            {100, 30, 30},
            {200, 60, 60},
            {500, 240, 240},
            {1000, 480, 480},
            {2000, 960, 960},
    };

    public static final int[][] NOT_VIP_DELAY_TIMES = {
            {30, 15},
            {40, 35},
            {50, 60},
            {60, 200},
            {70, 300},
            {80, 500},
            {90, 1000},
            {100, 2000}
    };

    public static final String COUNTRY_DELAY_TIMES_KEY = "cache:large_resource_delay_times";

    public static final String DELAY_TIMES_KEY = "cache:delay_times";

    public static final String WAIT_QUEUE_NAME_PREFIX = "waitQueue_";

    public static final String PROMPT_TEMPLATES_CACHE = "cache:PromptTemplates";

    /**
     * 数据库逻辑删除：未删除
     */
    public static final Integer DB_VALID = 0;

    /**
     * 数据库逻辑删除：已删除
     */
    public static final Integer DB_INVALID = 1;

    /**
     * 社区首页banner图缓存key
     */
    public static final String BANNER_IMG_CACHE_KEY = "cache:community_banner_img";

    /**
     * 社区首页banner(ios )图缓存key
     */
    public static final String BANNER_IMG_CACHE_KEY_IOS = "cache:community_banner_img:ios";


    /**
     * 观看广告间隔时间，2分钟
     */
    public static final long WATCH_AD_INTERVAL_TIME = 2 * 60;

    public static final String LAST_BEGIN_WATCH_AD_TIME_KEY = "cache:last_begin_watch_ad_time:";

    /**
     * 社区活动系统总数
     */
    public static final String COMM_ACTIVITY_PUBLIC = "commActivity:public";

    /**
     * 社区活动用户已读数量
     */
    public static final String COMM_ACTIVITY_USER_NUM = "commActivity:user_num";

    /**
     * PUSH打开统计redis缓存过期时间，单位小时
     */
    public static final Integer PUSH_OPEN_EXPIRED_TIME = 24;

    /**
     * PUSH打开统计redis缓存key前缀
     */
    public static final String PUSH_OPEN_KEY_PREFIX = "push:open:";

    /**
     * 每日UV统计redis缓存key前缀
     */
    public static final String DAILY_UNIQUE_VISITOR_KEY_PREFIX = "daily_unique_visitor:";

    /**
     * 批量去背景lumen预扣redis缓存key
     */
    public static final String BATCH_RMBG_LUMEN_ADVANCE_DEDUCT_KEY = "batch_rmbg_lumen_advance_deduct";

    /**
     * ttapi任务前缀
     */
    public static final String TTAPI_MARKID_PREFIX = "tmj-";
    /**
     * flux任务前缀
     */
    public static final String FLUX_MARKID_PREFIX = "flux-";
    /**
     * gemini任务前缀
     */
    public static final String GEMINI_MARKID_PREFIX = "gemini-";

    /**
     * 谷歌OAuth2.0登录idToken重复校验缓存key前缀
     */
    public static final String GOOGLE_LOGIN_USED_ID_TOKEN_KEY_PREFIX = "google_login_used_id_token:";

    /**
     * 模型规则配置缓存key
     */
    public static final String MODEL_RULES_CACHE_KEY = "cache:model_rule";

    /**
     * 用户未完成的免费试用任务key前缀
     */
    public static final String USER_NOT_FINISH_TASK_MODEL_FREE_TRIAL_KEY = "user_not_finish_model_task_free_trial:";

    /**
     * 用户未完成的付费试用任务key前缀
     */
    public static final String USER_NOT_FINISH_TASK_MODEL_PAY_TRIAL_KEY = "user_not_finish_model_task_pay_trial:";

    /**
     * 用户模型权益缓存key前缀
     */
    public static final String USER_MODEL_RIGHTS_CACHE_KEY = "cache:user_model_rights:";

    /**
     * 用户每日免费lumen弹窗控制缓存key
     */
    public static final String USER_DAILY_LUMEN_DIALOG_CONTROL_CACHE_KEY = "cache:user_daily_lumen_dialog_control";

    /**
     * 可用支付渠道缓存key前缀
     */
    public static final String AVAILABLE_PAYMENT_CHANNELS_KEY_PREFIX = "cache:available_payment_channels:";

    /**
     * 当前资源版本缓存key前缀
     */
    public static final String RESOURCES_VERSION_KEY_PREFIX = "cache:resources_version:";
}
