package com.lx.pl.enums;

/**
 * Midjourney操作类型枚举
 *
 * <AUTHOR>
 */
public enum MidjourneyActionType {

    // U操作（放大）
    UPSAMPLE1("upsample1", "U1按钮"),
    UPSAMPLE2("upsample2", "U2按钮"),
    UPSAMPLE3("upsample3", "U3按钮"),
    UPSAMPLE4("upsample4", "U4按钮"),

    // V操作（变化）
    VARIATION1("variation1", "V1按钮"),
    VARIATION2("variation2", "V2按钮"),
    VARIATION3("variation3", "V3按钮"),
    VARIATION4("variation4", "V4按钮"),

    // 变化操作
    HIGH_VARIATION("high_variation", "Vary (Strong)按钮"),
    LOW_VARIATION("low_variation", "Vary (Subtle)按钮"),

    // 放大操作
    UPSCALE2("upscale2", "Upscale(2x)按钮"),
    UPSCALE4("upscale4", "Upscale(4x)按钮"),
    UPSCALE_CREATIVE("upscale_creative", "Upscale(Creative)按钮"),
    UPSCALE_SUBTLE("upscale_subtle", "Upscale(Subtle)按钮"),

    // 缩放操作
    ZOOM_OUT_2("zoom_out_2", "Zoom Out 2x按钮"),
    ZOOM_OUT_1_5("zoom_out_1_5", "Zoom Out 1.5x按钮"),

    // 平移操作
    PAN_LEFT("pan_left", "⬅️按钮"),
    PAN_RIGHT("pan_right", "➡️按钮"),
    PAN_UP("pan_up", "⬆️按钮"),
    PAN_DOWN("pan_down", "⬇️按钮"),

    // 重新生成
    REROLL("reroll", "🔄按钮"),

    // 重做操作
    REDO_UPSCALE2("redo_upscale2", "Redo Upscale(2x)按钮"),
    REDO_UPSCALE4("redo_upscale4", "Redo Upscale(4x)按钮"),
    REDO_UPSCALE_SUBTLE("redo_upscale_subtle", "Redo Upscale(Subtle)按钮"),
    REDO_UPSCALE_CREATIVE("redo_upscale_creative", "Redo Upscale(Creative)按钮"),

    // 其他操作
    MAKE_SQUARE("make_square", "Make Square按钮");

    private final String action;
    private final String description;

    MidjourneyActionType(String action, String description) {
        this.action = action;
        this.description = description;
    }

    public String getAction() {
        return action;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据action字符串获取枚举
     */
    public static MidjourneyActionType fromAction(String action) {
        for (MidjourneyActionType type : values()) {
            if (type.action.equals(action)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown action: " + action);
    }
}
