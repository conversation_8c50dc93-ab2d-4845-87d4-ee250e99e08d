package com.lx.pl.enums;


public enum OriginCreate {

    create("create", "原始生图"),
    hiresFix("hiresFix", "高清修复"),
    removeBackground("removeBackground", "去背景"),
    picCreate("picCreate", "图生图"),
    localRedraw("localRedraw", "局部重绘"),
    lineRecolor("lineRecolor", "线稿上色"),
    enlargeImage("enlargeImage", "扩图"),
    customUpload("customUpload", "用户上传"),
    vary("vary", "图片微变"),
    crop("crop", "裁剪"),
    edit("edit", "图片编辑");

    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    OriginCreate(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        for (OriginCreate originCreate : OriginCreate.values()) {
            if (originCreate.getValue().equals(value)) {
                return originCreate.getLabel();
            }
        }
        return "";
    }
}
