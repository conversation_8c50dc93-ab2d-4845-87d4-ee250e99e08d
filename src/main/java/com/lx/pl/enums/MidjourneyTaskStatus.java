package com.lx.pl.enums;

/**
 * Midjourney任务状态枚举
 *
 * <AUTHOR>
 */
public enum MidjourneyTaskStatus {

    /**
     * 排队中
     */
    PENDING_QUEUE("PENDING_QUEUE", "排队中"),

    /**
     * 执行中
     */
    ON_QUEUE("ON_QUEUE", "执行中"),

    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),

    /**
     * 失败
     */
    FAILED("FAILED", "失败");

    private final String status;
    private final String description;

    MidjourneyTaskStatus(String status, String description) {
        this.status = status;
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态字符串获取枚举
     */
    public static MidjourneyTaskStatus fromStatus(String status) {
        for (MidjourneyTaskStatus taskStatus : values()) {
            if (taskStatus.status.equals(status)) {
                return taskStatus;
            }
        }
        throw new IllegalArgumentException("Unknown status: " + status);
    }

    /**
     * 判断是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }

    /**
     * 判断是否为进行中状态
     */
    public boolean isInProgress() {
        return this == PENDING_QUEUE || this == ON_QUEUE;
    }
}
