package com.lx.pl.controller.community.activity;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.community.activity.CommActivityPrizeSettings;
import com.lx.pl.db.mysql.community.entity.CommFile;
import com.lx.pl.db.mysql.community.entity.CommFileDetail;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.*;
import com.lx.pl.dto.community.activity.CommActivityDetailResult;
import com.lx.pl.dto.community.activity.CommActivityPostingResult;
import com.lx.pl.dto.community.activity.CommActivityResult;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.*;
import com.lx.pl.util.AESUtil;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Tag(name = "社区活动相关接口")
@Slf4j
@RestController
@RequestMapping("/api/comm-activity")
public class CommActivityController {

    @Autowired
    private CommImgService commImgService;

    @Autowired
    private CommActivityService commActivityService;

    @Autowired
    private CommActivityPrizeSettingsService commActivityPrizeSettingsService;

    @Autowired
    HistoryImgService historyImgService;

    @Autowired
    ImgService imgService;

    @Autowired
    GenService genService;

    @Operation(summary = "获取奖章列表")
    @PostMapping("/prize-list")
    @Authorization
    public R<List<CommActivityPrizeSettings>> listPrize() {
        List<CommActivityPrizeSettings> list = commActivityPrizeSettingsService.list();
        return R.success(list);
    }

    @Operation(summary = "获取活动列表")
    @PostMapping("/activity-list")
    @Authorization
    public R<PromptPageInfo<CommActivityResult>> getCommActivityList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @CurrentUser @Parameter(hidden = true) User user) {
        PromptPageInfo<CommActivityResult> commActivityList = commActivityService.getCommActivityList(pageNum, pageSize, user);
        return R.success(commActivityList);
    }

    @Operation(summary = "获取投稿中活动列表")
    @PostMapping("/post-list")
    @Authorization
    public R<List<CommActivityPostingResult>> getCommActivityPostList(
            @CurrentUser @Parameter(hidden = true) User user) {
        List<CommActivityPostingResult> commActivityList = commActivityService.getCommActivityPostingList(user);
        return R.success(commActivityList);
    }

    @Operation(summary = "获取活动详情")
    @PostMapping("/activity-detail")
    @Authorization
    public R<CommActivityDetailResult> getCommActivityDetail(
            @RequestParam("activityId") Long activityId,
            @CurrentUser @Parameter(hidden = true) User user) {
        CommActivityDetailResult commActivityDetail = commActivityService.getCommActivityDetail(activityId,user);
        return R.success(commActivityDetail);
    }

    @Operation(summary = "分页查询参赛图片")
    @GetMapping("/page-search")
    @Authorization
    public R<CommPageInfo<CommFile>> getCommActivityImgByPage(@Parameter(description = "注意当排序规则名称：Hot时候，lastFileId的参数需要拼装为：“点赞数#lastFileId”")
                                                              @RequestParam(value = "lastFileId", required = false) String lastFileId,
                                                              @RequestParam(value = "activityId") Long activityId,
                                                              @RequestParam("pageSize") Integer pageSize,
                                                              @RequestParam(value = "collationName")
                                                              @Parameter(description = "排序规则名称：Hot : 点赞数排序   Time : 时间")
                                                              String collationName,
                                                              @CurrentUser @Parameter(hidden = true) User user) throws Exception {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commImgService.getCommActivityImgByPage(activityId, lastFileId, pageSize, collationName, user));
    }

    @Operation(summary = "分页查询获奖图片")
    @GetMapping("/win-search")
    @Authorization
    public R<PromptPageInfo<CommFile>> getWinCommActivityImgByPage(@RequestParam(value = "activityId") Long activityId,
                                                                   @RequestParam("pageNum") Integer pageNum,
                                                                   @RequestParam("pageSize") Integer pageSize,
                                                                   @CurrentUser @Parameter(hidden = true) User user) throws Exception {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }

        return R.success(commImgService.getWinCommActivityImgByPage(activityId, pageNum, pageSize, user));
    }

    @Operation(summary = "删除社区投稿图片")
    @PostMapping("/delete-activity-img")
    @Authorization
    public R<Boolean> deleteActivityImg(@RequestParam("fileId") @Parameter(description = "原图片Id(非社区图片Id)") String fileId,
                                        @RequestParam("type") @Parameter(description = "删除图片类型 1:删除已公开的图片 2:删除审核审核中图片") Integer type,
                                        @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commImgService.deleteActivityImg(fileId, type, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "查询没有发布社区的历史图片")
    @GetMapping("/not-publish-img-list")
    @Authorization
    public R<PromptPageInfo<NotPublishImgResult>> getNotPublishImgList(@RequestParam("pageNum") Integer pageNum,
                                                                       @RequestParam("pageSize") Integer pageSize,
                                                                       @RequestParam(value = "classifyId") @Parameter(description = "收藏夹id 若要查询未收藏的数据传0")
                                                                       String classifyId, @CurrentUser @Parameter(hidden = true) User user) {
        if (pageSize > 200) {
            return R.fail(400, "pageSize exceeds 200 !");
        }
        return R.success(historyImgService.getNotPublishImgList(pageNum, pageSize, classifyId, user));
    }

    @Operation(summary = "查询用户某活动投稿图片")
    @GetMapping("/user-activity-posts-list")
    @Authorization
    public R<List<UserActivityPostsResult>> getUserActivityPostsList(@RequestParam(value = "activityId") String activityId
            , @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) {
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            platform  = "web";
        }
        return R.success(historyImgService.getUserActivityPostsList(activityId, user, platform));
    }

    @Operation(summary = "用户活动图片投稿")
    @PostMapping("/public-activity-img")
    @Authorization
    public R<Boolean> publicUserActivityActivityImg(@RequestBody CommActivityPostsDto commActivityPostsDto, @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = imgService.publicUserActivityImg(commActivityPostsDto, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }



}
